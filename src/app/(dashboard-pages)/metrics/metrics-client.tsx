"use client";

import React from "react";
import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"

// Import metrics components
import { SiteActivationWidget } from "@/components/metrics/site-activation-widget"
import { OnboardingPermissionsWidget } from "@/components/metrics/onboarding-permissions-widget"
import { PhiaClicksWidget } from "@/components/metrics/phia-clicks-widget"
import { HeartbeatWidget } from "@/components/metrics/heartbeat-widget"
import { RetentionWidget } from "@/components/metrics/retention-widget"
import { CohortRetentionTable } from "@/components/metrics/cohort-retention-table"

import type { MetricsDashboardData } from "./actions";

interface MetricsPageClientProps {
  data?: MetricsDashboardData;
}

export default function MetricsPageClient({ data }: MetricsPageClientProps) {
  return (
    <div className="@container/main flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Breadcrumbs and Separator first */}
      <Breadcrumb className="hidden font-medium md:flex">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/home">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Metrics</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Separator className="my-2 hidden md:block" />

      {/* Page Title */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Metrics Dashboard</h1>
        <p className="text-muted-foreground">
          Track user engagement, retention, and key performance indicators
        </p>
      </div>

      {/* Row 1: Site Activation (large) + Onboarding Permissions (chart) */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
        <SiteActivationWidget
          data={data?.siteActivation.map((row) => ({
            hostname: row.hostname ?? row.site_domain ?? 'unknown',
            phiaShown: row.phia_shown_count ?? 0,
            phiaClickedWithBookmark: row.phia_clicked_count ?? 0,
            activationRate: row.activation_rate ?? 0,
          }))}
          platform={data?.siteActivation?.[0]?.platform ?? 'IOS_SAFARI_EXTENSION'}
        />
        <OnboardingPermissionsWidget />
      </div>

      {/* Row 2: Phia Clicks (large number) + Heartbeat (4 values) */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
        <PhiaClicksWidget />
        <HeartbeatWidget />
      </div>

      {/* Row 3: Retention Chart (full width) */}
      <RetentionWidget />

      {/* Row 4: Cohort Retention Table (full width) */}
      <CohortRetentionTable />
    </div>
  );
}
