import { createClient } from "@/supabase/client/server";
import { redirect } from "next/navigation";
import MetricsPageClient from "./metrics-client";
import { fetchMetricsDashboard } from "./actions";

export default async function MetricsPage() {
  // Server-side authentication check
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    redirect('/sign-in');
  }

  const data = await fetchMetricsDashboard();

  // If authenticated, render the client component with data
  return <MetricsPageClient data={data} />;
}
