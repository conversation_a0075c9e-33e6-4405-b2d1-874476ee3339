"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MoreHorizontal, Download, Filter, Calendar } from "lucide-react";
import { cn } from "@/lib/utils";

export interface CohortRetentionTableProps {
  data?: {
    weekNumber: number;
    cohortStartDate: string;
    retentionRate: number;
    cohortSize: number;
    activeUsers: number;
    weekStartDate: string;
    weekEndDate: string;
    cohortName: string;
  }[];
}

// Mock data based on the cohort retention table image
const cohortData = [
  {
    cohortWeekStart: "06/24/25",
    newUsersFullyOnboarded: 10714,
    week1RetainedPercent: 24.40,
    week2RetainedPercent: 22.18,
    week3RetainedPercent: 19.74,
    week4RetainedPercent: 16.77,
    week5RetainedPercent: 13.78
  },
  {
    cohortWeekStart: "06/17/25",
    newUsersFullyOnboarded: 9896,
    week1RetainedPercent: 21.87,
    week2RetainedPercent: 20.15,
    week3RetainedPercent: 16.34,
    week4RetainedPercent: 13.98,
    week5RetainedPercent: 8.80
  },
  {
    cohortWeekStart: "06/10/25",
    newUsersFullyOnboarded: 4558,
    week1RetainedPercent: 22.75,
    week2RetainedPercent: 19.85,
    week3RetainedPercent: 16.17,
    week4RetainedPercent: 13.84,
    week5RetainedPercent: 7.53
  },
  {
    cohortWeekStart: "06/03/25",
    newUsersFullyOnboarded: 2516,
    week1RetainedPercent: 24.85,
    week2RetainedPercent: 19.12,
    week3RetainedPercent: 14.23,
    week4RetainedPercent: 6.74,
    week5RetainedPercent: 0
  },
  {
    cohortWeekStart: "05/27/25",
    newUsersFullyOnboarded: 4318,
    week1RetainedPercent: 22.29,
    week2RetainedPercent: 9.46,
    week3RetainedPercent: 7.40,
    week4RetainedPercent: 0,
    week5RetainedPercent: 0
  },
  {
    cohortWeekStart: "05/20/25",
    newUsersFullyOnboarded: 4484,
    week1RetainedPercent: 19.30,
    week2RetainedPercent: 10.02,
    week3RetainedPercent: 0,
    week4RetainedPercent: 0,
    week5RetainedPercent: 0
  },
  {
    cohortWeekStart: "05/13/25",
    newUsersFullyOnboarded: 5541,
    week1RetainedPercent: 0,
    week2RetainedPercent: 0,
    week3RetainedPercent: 0,
    week4RetainedPercent: 0,
    week5RetainedPercent: 0
  }
];

// Helper function to get color based on retention percentage
const getRetentionColor = (percentage: number): string => {
  if (percentage === 0) return "bg-muted text-muted-foreground";
  if (percentage < 10) return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800";
  if (percentage < 15) return "bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-800";
  if (percentage < 20) return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800";
  if (percentage < 25) return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800";
  return "bg-green-200 dark:bg-green-900/50 text-green-800 dark:text-green-200 border border-green-300 dark:border-green-700";
};

export function CohortRetentionTable({ data }: CohortRetentionTableProps) {
  const [selectedCohortType, setSelectedCohortType] = useState("weekly");
  const [selectedDateRange, setSelectedDateRange] = useState("last_8_weeks");
  const [selectedMetric, setSelectedMetric] = useState("retention_rate");
  const [activeView, setActiveView] = useState("table");

  // Use real data if available, otherwise fall back to mock data
  const tableData = data || [];
  const cohortInfo = data && data.length > 0 ? {
    name: data[0].cohortName,
    startDate: data[0].cohortStartDate,
    totalWeeks: data.length
  } : null;

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-base font-medium">Cohort Retention Table</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {cohortInfo ? `${cohortInfo.name} - ${cohortInfo.totalWeeks} weeks tracked` : 'Weekly cohort retention data'}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Download className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Cohort Data Table */}
          <div className="space-y-4">
            <div className="grid grid-cols-5 gap-4 text-xs font-medium text-muted-foreground border-b border-border pb-3">
              <span>Week</span>
              <span className="text-right">Active Users</span>
              <span className="text-right">Retention Rate</span>
              <span className="text-right">Week Period</span>
              <span className="text-right">Cohort Size</span>
            </div>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {tableData.map((row, index) => (
                <div key={index} className="grid grid-cols-5 gap-4 text-sm py-2 hover:bg-muted/30 rounded">
                  <span className="font-medium">Week {row.weekNumber}</span>
                  <span className="text-right">{row.activeUsers.toLocaleString()}</span>
                  <span className="text-right">{row.retentionRate.toFixed(2)}%</span>
                  <span className="text-right text-xs text-muted-foreground">
                    {new Date(row.weekStartDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} -
                    {new Date(row.weekEndDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </span>
                  <span className="text-right">{row.cohortSize || 'N/A'}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
