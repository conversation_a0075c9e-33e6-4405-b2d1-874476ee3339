"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";

export interface RetentionWidgetProps {
  data?: {
    weekNumber: number;
    cohortStartDate: string;
    retentionRate: number;
    cohortSize: number;
    activeUsers: number;
    weekStartDate: string;
    weekEndDate: string;
  }[];
}

// Mock data based on the fifth image - Retention curve
const retentionData = {
  title: "Week 9 Retention (6/19 - 6/28)",
  subtitle: "Churn",
  description: "The number of select calendar weeks later your Users come back and did it.",
  chartData: [
    { week: "Week", percentage: 92.3 },
    { week: "Week 1", percentage: 82.5 }
  ],
  tableData: [
    { date: "Jun 19, 2025", totalProfiles: 640, week1: "91.26%", weekValue: "63.35%" },
    { date: "Jun 20, 2025", totalProfiles: 889, week1: "97.55%", weekValue: "51.86%" },
    { date: "Jun 21, 2025", totalProfiles: 1118, week1: "97.32%", weekValue: "85.24%" },
    { date: "Jun 22, 2025", totalProfiles: 1435, week1: "97.56%", weekValue: "86.3%" }
  ]
};

export function RetentionWidget({ data }: RetentionWidgetProps) {
  // Use real data if available, otherwise fall back to mock data
  const retentionChartData = data ? data.map((row) => ({
    week: `Week ${row.weekNumber}`,
    retention: row.retentionRate,
    activeUsers: row.activeUsers,
    weekStart: row.weekStartDate,
    weekEnd: row.weekEndDate
  })) : retentionData.chartData;

  const cohortInfo = data && data.length > 0 ? {
    startDate: data[0].cohortStartDate,
    totalWeeks: data.length,
    initialUsers: data[0].activeUsers
  } : null;

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-base font-medium">Weekly Retention Analysis</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {cohortInfo ? `Cohort starting ${new Date(cohortInfo.startDate).toLocaleDateString()} - ${cohortInfo.totalWeeks} weeks tracked` : 'Retention tracking over time'}
          </p>
        </div>

      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Key Metrics Summary */}
          {cohortInfo && (
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted/20 rounded-lg">
                <div className="text-2xl font-bold text-primary">{cohortInfo.initialUsers.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Initial Active Users</div>
              </div>
              <div className="text-center p-4 bg-muted/20 rounded-lg">
                <div className="text-2xl font-bold text-primary">{cohortInfo.totalWeeks}</div>
                <div className="text-sm text-muted-foreground">Weeks Tracked</div>
              </div>
              <div className="text-center p-4 bg-muted/20 rounded-lg">
                <div className="text-2xl font-bold text-primary">
                  {retentionChartData.length > 1 ? `${retentionChartData[retentionChartData.length - 1].retention.toFixed(1)}%` : '0%'}
                </div>
                <div className="text-sm text-muted-foreground">Latest Week Retention</div>
              </div>
            </div>
          )}

          {/* Retention Data Table */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">Weekly Retention Breakdown</h4>
            <div className="grid grid-cols-4 gap-4 text-xs font-medium text-muted-foreground border-b border-border pb-3">
              <span>Week</span>
              <span className="text-right">Active Users</span>
              <span className="text-right">Retention Rate</span>
              <span className="text-right">Week Period</span>
            </div>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {retentionChartData.map((row, index) => (
                <div key={index} className="grid grid-cols-4 gap-4 text-sm py-2 hover:bg-muted/30 rounded">
                  <span className="font-medium">{row.week}</span>
                  <span className="text-right">{row.activeUsers.toLocaleString()}</span>
                  <span className="text-right">{row.retention.toFixed(2)}%</span>
                  <span className="text-right text-xs text-muted-foreground">
                    {new Date(row.weekStart).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} -
                    {new Date(row.weekEnd).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
