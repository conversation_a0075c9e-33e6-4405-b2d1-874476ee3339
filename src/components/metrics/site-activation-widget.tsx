"use client";

import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MoreHorizontal, Download, Filter } from "lucide-react";

// Mock data based on the first image
const siteActivationData = [
  { hostname: "www.amazon.com", phiaShown: 443554, phiaClickedWithBookmark: 19196, activationRate: 4.14 },
  { hostname: "www.nordstrom.com", phiaShown: 158622, phiaClickedWithBookmark: 10139, activationRate: 6.82 },
  { hostname: "www.quince.com", phiaShown: 156399, phiaClickedWithBookmark: 8896, activationRate: 5.93 },
  { hostname: "www.aritzia.com", phiaShown: 118965, phiaClickedWithBookmark: 6978, activationRate: 5.86 },
  { hostname: "www.jcrew.com", phiaShown: 109161, phiaClickedWithBookmark: 4598, activationRate: 4.21 },
  { hostname: "www.thereformation.com", phiaShown: 103638, phiaClickedWithBookmark: 10692, activationRate: 10.37 },
  { hostname: "www.revolve.com", phiaShown: 98122, phiaClickedWithBookmark: 9167, activationRate: 9.52 },
  { hostname: "skims.com", phiaShown: 91274, phiaClickedWithBookmark: 3190, activationRate: 3.49 },
  { hostname: "www.target.com", phiaShown: 88582, phiaClickedWithBookmark: 2917, activationRate: 3.29 },
  { hostname: "www.etsy.com", phiaShown: 83490, phiaClickedWithBookmark: 4564, activationRate: 5.44 },
  { hostname: "www.ebay.com", phiaShown: 83866, phiaClickedWithBookmark: 8649, activationRate: 10.31 },
  { hostname: "www2.hm.com", phiaShown: 83617, phiaClickedWithBookmark: 2574, activationRate: 3.07 }
];

function calculateTotals(dataArray: typeof siteActivationData) {
  const totalPhiaShown = dataArray.reduce((sum, item) => sum + item.phiaShown, 0);
  const totalPhiaClicked = dataArray.reduce((sum, item) => sum + item.phiaClickedWithBookmark, 0);
  const overallActivationRate = (totalPhiaClicked / totalPhiaShown) * 100;
  return { totalPhiaShown, totalPhiaClicked, overallActivationRate };
}

export interface SiteActivationWidgetProps {
  data?: {
    hostname: string | null;
    phiaShown: number;
    phiaClickedWithBookmark: number;
    activationRate: number;
  }[];
  platform?: string;
}

export function SiteActivationWidget({ data, platform = "IOS_SAFARI_EXTENSION" }: SiteActivationWidgetProps) {
  const [selectedPlatform, setSelectedPlatform] = useState(platform);
  const [selectedMetric, setSelectedMetric] = useState("activation_rate");
  const [activeTab, setActiveTab] = useState("table");

  const mergedData = data ?? siteActivationData;
  const { totalPhiaShown, totalPhiaClicked, overallActivationRate } = calculateTotals(mergedData as any);

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-base font-medium">New Site Activation Rate Broken Down by Site</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Platform: {selectedPlatform}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Download className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Filters and Controls */}
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Select platform" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="IOS_SAFARI_EXTENSION">iOS Safari Extension</SelectItem>
                  <SelectItem value="CHROME_EXTENSION">Chrome Extension</SelectItem>
                  <SelectItem value="ALL_PLATFORMS">All Platforms</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-auto">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="table">Table</TabsTrigger>
                <TabsTrigger value="chart">Chart</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Summary row */}
          <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
            <span className="font-medium text-foreground">Overall</span>
            <div className="flex gap-8 text-sm text-muted-foreground">
              <span>{totalPhiaShown.toLocaleString()}</span>
              <span>{totalPhiaClicked.toLocaleString()}</span>
              <span className="font-medium text-foreground">{overallActivationRate.toFixed(2)}%</span>
            </div>
          </div>

          {/* Content based on active tab */}
          <Tabs value={activeTab} className="w-full">
            <TabsContent value="table" className="space-y-4">
              {/* Header */}
              <div className="grid grid-cols-4 gap-4 text-xs font-medium text-muted-foreground border-b border-border pb-3">
                <span>hostname</span>
                <span className="text-right">Phia Shown</span>
                <span className="text-right">Phia clicked with bookmark</span>
                <span className="text-right">Activation Rate</span>
              </div>

              {/* Data rows */}
              <div className="space-y-1 max-h-80 overflow-y-auto">
                {mergedData.map((item, index) => (
                  <div key={index} className="grid grid-cols-4 gap-4 text-sm py-3 hover:bg-muted/30 rounded-md transition-colors">
                    <a href={`https://${item.hostname}`} className="text-primary hover:underline truncate font-medium">
                      {item.hostname}
                    </a>
                    <span className="text-right text-muted-foreground">{item.phiaShown.toLocaleString()}</span>
                    <span className="text-right text-muted-foreground">{item.phiaClickedWithBookmark.toLocaleString()}</span>
                    <span className="text-right font-semibold text-foreground">{item.activationRate.toFixed(2)}%</span>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="chart" className="space-y-4">
              <div className="h-80 bg-muted/20 rounded-lg border p-4">
                <div className="h-full flex flex-col">
                  <h4 className="text-sm font-medium mb-4">Activation Rate by Site</h4>
                  <div className="flex-1 flex items-end justify-between gap-2">
                    {mergedData.slice(0, 8).map((item, index) => {
                      const height = (item.activationRate / 12) * 100; // Scale to max 12%
                      return (
                        <div key={index} className="flex flex-col items-center flex-1">
                          <div
                            className="w-full bg-primary rounded-t transition-all hover:bg-primary/80"
                            style={{ height: `${Math.max(height, 5)}%` }}
                            title={`${item.hostname}: ${item.activationRate.toFixed(2)}%`}
                          />
                          <span className="text-xs text-muted-foreground mt-2 truncate w-full text-center">
                            {item.hostname.replace('www.', '').split('.')[0]}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
}
